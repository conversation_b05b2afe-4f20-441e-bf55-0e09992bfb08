/* Custom styles for promo page */
        .promo-header {
            background: #fff;
            padding: 40px 0;
            /* box-shadow: 0 2px 10px rgba(0,0,0,0.1); */
            max-width: 1500px;
            margin: 0 auto;
        }

        .promo-hero {
            /* background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #DAA900 100%); */
            color: white;
            padding: 80px 60px;
            position: relative;
            overflow: hidden;
            background-image: url('../img/promo/cover_bg.jpg');
            background-size: cover;
            background-position: top right;
        }

        /* .promo-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            z-index: 1;
        } */

        .promo-hero .container {
            position: relative;
            z-index: 2;
        }

        .promo-hero h1 {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .promo-hero .highlight {
            background: linear-gradient(220.85deg, #DAA900 -1.27%, #0080BB 94.43%);
            padding: 0;
            border-radius: 5px;
            display: inline-block;
        }

        .promo-hero .lead {
            font-size: 1.8rem;
            margin-bottom: -5px;
        }

        .promo-hero .lead-2nd {
            font-size: 1.2em;
            font-weight: 300;
        }

        .price-text {
            font-size: 4rem;
            font-weight: 700;
            font-style: italic;
            margin: 30px 0 -25px 0;
        }

        .delivery-text {
            font-size: 3.4rem;
            font-weight: 500;
            font-style: italic;
            margin-bottom: 10px;
        }

        .promo-description {
            font-size: 1.1rem;
            margin-bottom: 50px;
            font-weight: 300;
            text-align: justify;
        }

        .btn-cta {
            background: #DAA900;
            border: none;
            padding: 10px 20px 7px 20px;
            font-size: 1.2rem;
            font-weight: 500;
            text-transform: uppercase;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #fff;
        }

        .btn-cta:hover {
            background: #c19700;
            /* transform: translateY(-2px); */
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .loyalty-section {
            padding: 80px 0;
            /* background: #f8f9fa; */
        }

        .loyalty-card {
            background-color: #0080BB;
            /* border-radius: 15px; */
            padding: 50px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        /* .loyalty-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            z-index: 1;
        } */

        .loyalty-card .row {
            position: relative;
            z-index: 2;
        }

        .loyalty-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            /* letter-spacing: 2px; */
        }

        .loyalty-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 10px;
            /* opacity: 0.95; */
            font-weight: 300;
            text-align: justify;
            max-width: 780px;
        }

        .discount-badge {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            color: #1976d2;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
            max-width: 250px;
            margin: 0 auto;
        }

        .discount-badge::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 15px solid white;
        }

        .discount-number {
            font-size: 4rem;
            font-weight: 900;
            line-height: 1;
            color: #DAA900;
            margin-bottom: 10px;
        }

        .discount-number .percent {
            font-size: 2.5rem;
            vertical-align: top;
        }

        .discount-label .additional {
            font-size: 1rem;
            font-weight: 400;
            color: #666;
        }

        .discount-label .discount-text {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1976d2;
            letter-spacing: 1px;
        }
        .loyalty-section img {
            max-width: 250px;
        }

        .why-choose-section {
            padding: 80px 0;
            background: #fff;
        }

        .why-choose-section h2 {
            font-size: 3rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 60px;
            color: #000;
        }

        .feature-card {
            text-align: center;
            padding: 40px 20px;
            margin-bottom: 40px;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .feature-card p {
            text-align: justify;
            font-weight: 300;
        }

        .feature-card:hover {
            /* transform: translateY(-5px); */
        }

        .feature-card img {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        .feature-card h4 {
            font-weight: 600;
            margin-bottom: 25px;
            text-transform: uppercase;
            height: 50px;
        }

        .feature-card.teal h4 { color: #13BFC8; }
        .feature-card.blue h4 { color: #427FD6; }
        .feature-card.purple h4 { color: #6B30D5; }
        .feature-card.pink h4 { color: #D7004D; }
        .feature-card.orange h4 { color: #E95121; }
        .feature-card.gold h4 { color: #E8A900; }

        .icon-container {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .what-we-serve-section {
            padding: 80px 0;
            /* background: #f8f9fa; */
        }

        .serve-title {
            font-size: 3rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 30px;
            letter-spacing: 1px;
        }

        .serve-subtitle {
            font-size: 1.1rem;
            color: #666;
            /* max-width: 800px; */
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 300;
        }

        .services-showcase {
            margin-top: 60px;
        }

        .services-bg {
            /* background: linear-gradient(135deg, rgba(255,193,7,0.9) 0%, rgba(255,152,0,0.9) 50%, rgba(230,126,34,0.9) 100%);
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120,119,198,0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,119,48,0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120,219,226,0.3) 0%, transparent 50%); */
            padding: 60px 0;
            /* border-radius: 20px; */
            position: relative;
            overflow: hidden;
            background-image: url('../img/promo/server_bg.jpg');
            background-repeat: no-repeat;
        }

        /* .services-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.1);
            z-index: 1;
        } */

        .services-bg .container {
            position: relative;
            z-index: 2;
        }

        .services-title {
            color: white;
            font-size: 2rem;
            font-weight: 500;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 40px;
        }

        .service-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            /* box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease; */
            height: 100%;
        }

        .service-card:hover {
            /* transform: translateY(-5px); */
            /* box-shadow: 0 15px 40px rgba(0,0,0,0.2); */
        }

        .service-image {
            height: 200px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
            /* background: #f8f9fa; */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .service-svg {
            width: 100%;
            height: 100%;
            max-width: 250px;
        }

        .service-label {
            background: #fff;
            color: #000;
            padding: 5px 20px;
            /* border-radius: 8px; */
            font-weight: 500;
            text-align: center;
            font-size: 1rem;
            border: 1px solid #333;
            transition: all 0.3s ease;
            width: 100%;
            display: block;
            text-decoration: none;
        }

        /* .service-label.active {
            background: #ffc107;
            color: #333;
            border-color: #ffb300;
        } */

        .service-label:hover {
            background: #DAA900;
            color: #fff;
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            background: #f8f9fa;
        }

        .modal-header {
            padding: 20px 30px 0;
        }

        .modal-body {
            padding: 20px 40px 40px;
        }

        .modal-title {
            color: #1976d2 !important;
            font-size: 2.2rem;
            letter-spacing: 1px;
        }

        .modal-subtitle {
            color: #666 !important;
            font-size: 1.2rem;
        }

        .form-control-lg {
            padding: 15px 20px;
            font-size: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            transition: all 0.3s ease;
        }

        .form-control-lg:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
            background: white;
        }

        .form-control-lg::placeholder {
            color: #999;
            font-weight: 400;
        }

        .btn-warning {
            background: #ffc107;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-warning:hover {
            background: #ffb300;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
        }

        .form-control.is-valid {
            border-color: #28a745;
        }

        .final-cta-section {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #DAA900 100%);
            background-image: url('../img/promo/footer_bg.png');
            background-size: cover;
            background-position: center right;
            color: white;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .final-cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            z-index: 1;
        }

        .final-cta-section .container {
            position: relative;
            z-index: 2;
        }

        .final-cta-section h2 {
            font-size: 3rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }

        .cta-tags {
            text-align: center;
            margin-bottom: 0px;
        }

        .cta-tag {
            background: rgba(255,255,255,1);
            color: #333;
            padding: 5px;
            margin: 5px;
            /* border-radius: 25px; */
            display: inline-block;
            font-style: italic;
        }

        .rocket-icon {
            position: absolute;
            right: 10%;
            top: 50%;
            transform: translateY(-50%);
            width: 150px;
            height: auto;
        }

        @media (max-width: 768px) {
            .promo-hero h1 { font-size: 2.5rem; }
            .price-text { font-size: 2.5rem; }
            .delivery-text { font-size: 1.8rem; }
            .why-choose-section h2 { font-size: 2.2rem; }
            .final-cta-section h2 { font-size: 2.2rem; }
            .rocket-icon { display: none; }

            .loyalty-card {
                padding: 30px 20px;
                text-align: center;
            }

            .loyalty-title {
                font-size: 2rem;
                margin-bottom: 20px;
            }

            .loyalty-text {
                font-size: 1rem;
                margin-bottom: 30px;
            }

            .discount-badge {
                margin-top: 20px;
                max-width: 200px;
            }

            .discount-number {
                font-size: 3rem;
            }

            .serve-title {
                font-size: 2.2rem;
            }

            .serve-subtitle {
                font-size: 1rem;
                padding: 0 15px;
            }

            .services-bg {
                padding: 40px 15px;
                margin: 0 15px;
                border-radius: 15px;
            }

            .services-title {
                font-size: 1.5rem;
                margin-bottom: 30px;
            }

            .service-card {
                margin-bottom: 20px;
            }

            .service-image {
                height: 150px;
            }

            .modal-title {
                font-size: 1.8rem;
            }

            .modal-subtitle {
                font-size: 1rem;
            }

            .modal-body {
                padding: 20px 25px 30px;
            }

            .form-control-lg {
                padding: 12px 15px;
                font-size: 0.95rem;
            }
        }