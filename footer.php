<footer>
        <div class="container">
            <div class="row">
                <div class="col-md-auto">
                    <img src="<?php bloginfo('template_url'); ?>/img/logo-icon.png" alt="Attest Design Logo">
                </div>
                <div class="col">
                    <h6>Corporate Office</h6>
                    <p>26/2 Central Road, Ground Floor,<br>
                        New Market, Dhaka-1205, Bangladesh</p>
                    <h6>Administrative Office</h6>
                    <p>27, Old Gloucester Street,<br>
                        London, WC1N 3AX, United Kingdom</p>
                    <h5><img src="<?php bloginfo('template_url'); ?>/img/ico-phone.svg" alt="Phone icon"> +88 01332 53 31 77</h5>
                    <h5><img src="<?php bloginfo('template_url'); ?>/img/ico-email.svg" alt="Email icon"> <EMAIL></h5>
                </div>
                <div class="col footer-payment">
                    <h6>Payment Options</h6>
                    <img src="<?php bloginfo('template_url'); ?>/img/paymentsv2.svg" alt="Accepted payment methods including credit cards and digital payments">

                    <p class="pt-3">Follow us<br>
                        <a href="https://www.facebook.com/people/Attest-Design-Ltd/61574719254306/"><img src="<?php bloginfo('template_url'); ?>/img/fb.png" alt="Follow Attest Design on Facebook"></a>
                        <a href="https://www.instagram.com/attest_design/"><img src="<?php bloginfo('template_url'); ?>/img/insta.png" alt="Follow Attest Design on Instagram"></a>
                        <a href="https://www.linkedin.com/company/attestdesignltdglobal/"><img src="<?php bloginfo('template_url'); ?>/img/in.png" alt="Follow Attest Design on LinkedIn"></a>
                        <a href="https://www.pinterest.com/AttestDesign2025/"><img src="<?php bloginfo('template_url'); ?>/img/pin.png" alt="Follow Attest Design on Pinterest"></a>
                        <a href="https://www.youtube.com/@AttestDesign"><img src="<?php bloginfo('template_url'); ?>/img/yt.png" alt="Follow Attest Design on YouTube"></a>
                        <a href="https://x.com/Attest_Design"><img src="<?php bloginfo('template_url'); ?>/img/x.png" alt="Follow Attest Design on X (Twitter)"></a></p>

                </div>
                <div class="col footer-menu">
                    <h6>EasyGo to-</h6>
                    <ul>
                        <li><a href="https://dashboard.attestdesign.com/contact-us">CONTACT US</a></li>
                        <li><a href="<?php echo get_option('home'); ?>/blog">BLOG</a></li>
                        <li><a href="<?php echo get_option('home'); ?>/faq">FAQ</a></li>
                        <li><a href="<?php echo get_option('home'); ?>/terms-and-conditions">TERMS AND CONDITIONS</a></li>
                        <li><a href="<?php echo get_option('home'); ?>/privacy-policy">PRIVACY POLICY</a></li>
                        <li><a href="<?php echo get_option('home'); ?>/data-security-policy">DATA SECURITY POLICY</a></li>
                    </ul>
                </div>
                <div class="col-md-auto footer-whatsapp">
                    <a href="https://wa.me/8801332533177"><img src="<?php bloginfo('template_url'); ?>/img/whatsapp.png" alt="Contact Attest Design via WhatsApp"></a>
                </div>
            </div>
            <div class="row pt-5">
                <div class="col text-center">
                    <p>© Attest Design | 2024</p>
                </div>
            </div>
        </div>
    </footer>

    <?php wp_footer(); ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <script>
    // Enhanced search functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Handle search form submissions
        const searchForms = document.querySelectorAll('form[role="search"]');

        searchForms.forEach(function(form) {
            const searchInput = form.querySelector('input[name="s"]');

            if (searchInput) {
                // Submit form when Enter is pressed
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        if (this.value.trim() !== '') {
                            form.submit();
                        }
                    }
                });

                // Optional: Add search icon click functionality
                searchInput.addEventListener('click', function() {
                    this.focus();
                });
            }
        });
    });
    </script>
</body>

</html>