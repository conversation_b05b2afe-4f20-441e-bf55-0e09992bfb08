<?php get_header(); ?>

<?php /* <section id="page-head-bg" class="page-head-bg-portfolio">
    <div class="container">
        <div class="row">
            <div class="col text-center">
                <h1>ADverse BLOG</h1>
                <p>Welcome to our prestigious blog. Here, you will have the opportunity to gain a unique and insightful perspective on a diverse range of topics. We cordially invite you to peruse our extensive collection of articles and explore the wealth of knowledge therein.
                </p>
            </div>
        </div>
    </div>
</section>

<section id="util-nav">
    <div class="container">
        <div class="row">
            <div class="col"></div>
            <div class="col">
                <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="cus-search form-control" placeholder="Search topic or keyword" aria-label="Search" name="s" value="<?php echo get_search_query(); ?>">
                </form>
            </div>
        </div>
    </div>
</section> */ ?>

<section id="image-layout-col" class="blog-post">
    <div class="container">
        <div class="container">

            <div class="row">
                <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
                <div class="col">
                    <p>by <?php the_author() ?> • <?php the_time('F jS, Y') ?></p>

                    <h1><?php the_title(); ?></h1>

                    <img class="img-fluid mx-auto d-block post-img-size" src="<?php the_post_thumbnail_url('full');?>" alt="<?php echo esc_attr(get_the_title()); ?> - Featured image">

                    <?php the_content(); ?>


                </div>

                <?php endwhile; ?>

                <?php else : ?>

                    <h2>Not Found</h2>

                <?php endif; ?>


            </div>

        </div>
    </div>
    </div>
</section>

<?php get_footer(); ?>

