<?php get_header(); ?>

<section id="page-head-bg" class="page-head-bg-portfolio">
    <div class="container">
        <div class="row">
            <div class="col text-center">
                <h1>ADverse BLOG</h1>
                <p>Welcome to our prestigious blog. Here, you will have the opportunity to gain a unique and insightful perspective on a diverse range of topics. We cordially invite you to peruse our extensive collection of articles and explore the wealth of knowledge therein.
                </p>
            </div>
        </div>
    </div>
</section>

<section id="util-nav">
    <div class="container">
        <div class="row">
            <div class="col">
                <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="cus-search form-control" placeholder="Search topic or keyword" aria-label="Search" name="s" value="<?php echo get_search_query(); ?>">
                </form>
            </div>
            <div class="col"></div>
            <div class="col">
                <form>
                    <div class="form-group">
                        <select class="form-control" id="category-dropdown" onchange="navigateToCategory()">
                            <option value="">Category</option>
                            <?php
                            $categories = get_categories(array(
                                'hide_empty' => 0, // Show empty categories too
                                'exclude' => array(1, 2),
                                'orderby' => 'name',
                                'order' => 'ASC'
                            ));

                            foreach($categories as $category) {
                                echo '<option value="' . get_category_link($category->term_id) . '">' . $category->name . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </form>

                <script>
                function navigateToCategory() {
                    var dropdown = document.getElementById('category-dropdown');
                    var url = dropdown.options[dropdown.selectedIndex].value;
                    if (url) {
                        window.location.href = url;
                    }
                }
                </script>
            </div>
        </div>
    </div>
</section>

<section id="image-layout-col">
    <div class="container">
        <div class="container layout-col-middle layout-col-blog">

            <div class="row layout-col">

            <?php if (have_posts()) : ?>

                <?php while (have_posts()) : the_post(); ?>

                <div class="col-6">
                    <a href="<?php the_permalink() ?>">
                        <img class="img-fluid" src="<?php the_post_thumbnail_url('full');?>" alt="<?php echo esc_attr(get_the_title()); ?> - Featured image">
                    </a>
                    <h6>
                        <a class="link-color-blue" href="<?php the_permalink() ?>">
                            <?php the_title(); ?>
                        </a>
                        </h6>
                    <p><?php the_excerpt(); ?></p>
                    <p>by <?php the_author() ?> • <?php the_time('F jS, Y') ?></p>
                    <p><a href="<?php the_permalink() ?>">Read more...</a></p>
                </div>

                <?php endwhile; ?>

				<?php //include (TEMPLATEPATH . '/inc/nav.php' ); ?>

            <?php else : ?>

                <h2>Nothing found</h2>

            <?php endif; ?>

            </div>

        </div>

        <?php /* <div class="row row-nav">
            <div class="col">
                <nav aria-label="Page navigation example">
                    <ul class="pagination">
                        <li class="page-item cus-nav-prev">
                            <a class="page-link" href="#" tabindex="-1"><img src="<?php bloginfo('template_url'); ?>/img/arrow-left.svg"> Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item cus-nav-next">
                            <a class="page-link" href="#">Next <img src="<?php bloginfo('template_url'); ?>/img/arrow-right.svg"></a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="col text-end">
                <a href="#page-top" class="back-top">
                    <img src="<?php bloginfo('template_url'); ?>/img/back-top.svg">
                </a>
            </div>
        </div> */ ?>
    </div>
    </div>
</section>

<?php get_footer(); ?>
