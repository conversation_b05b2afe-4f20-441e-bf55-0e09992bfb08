<?php get_header(); ?>

<section id="page-head-bg" class="page-head-bg-portfolio">
    <div class="container">
        <div class="row">
            <div class="col text-center">
                <h1>ADverse PORTFOLIO</h1>
                <p>Welcome to our portfolio! Browse our impressive projects to see our capabilities and dedication to delivering high-quality results. Thank you for considering our work; we look forward to working with you!</p>
            </div>
        </div>
    </div>
</section>

<?php /* <section id="util-nav">
    <div class="container">
        <div class="row">
            <div class="col">
                <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="cus-search form-control" placeholder="Search topic or keyword" aria-label="Search" name="s" value="<?php echo get_search_query(); ?>">
                </form>
            </div>
            <div class="col"></div>
            <div class="col">
                <form>
                    <div class="form-group">
                        <select class="form-control">
                            <option>Cetegory</option>
                            <option value="cat1">Clipping Path</option>
                            <option value="cat2">Background Removal</option>
                            <option value="cat3">Ghost Mannequin</option>
                            <option value="cat1">Image Masking</option>
                            <option value="cat2">Photo Retouching Service</option>
                            <option value="cat3">Color Correction</option>
                            <option value="cat1">Photography Post-Processing</option>
                            <option value="cat2">E-commerce Product Image Editing</option>
                            <option value="cat3">Video Production</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section> */?>

<section id="image-layout-col">
    <div class="container">
        <div class="container text-center layout-col-middle layout-col-portfolio">

            <div class="row layout-col">

                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Clipping-Path.jpg" alt="Professional clipping path service example showing precise object isolation and background removal">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Color-Correction.jpg" alt="Color correction service example demonstrating enhanced image colors and professional photo adjustment">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Fashion-photo-clipping-path.jpg" alt="Fashion photography clipping path service showing model isolation and background removal for fashion industry">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Hair-Masking.jpg" alt="Hair masking service example showing detailed hair edge refinement and complex background removal">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Image-manipulation.jpg" alt="Image manipulation service example showcasing creative photo editing and digital enhancement techniques">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Image-Masking.jpg" alt="Image masking service example demonstrating advanced selection techniques for complex objects">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Image-Resizing.jpg" alt="Image resizing service example showing optimized dimensions for various platforms and requirements">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Interior-photo-retouch.jpg" alt="Interior photo retouching service example showing enhanced architectural photography and space optimization">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Jewelry-Photo-Clipping-Path.jpg" alt="Jewelry photo clipping path service demonstrating precise gemstone and metal isolation for luxury products">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Model-High-End-Photo-Retouching.jpg" alt="High-end model photo retouching service showing professional beauty enhancement and skin refinement">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Photo-Color-Correction.jpg" alt="Photo color correction service example demonstrating professional color grading and tone adjustment">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Photography-Post-Processing.jpg" alt="Photography post-processing service example showcasing comprehensive photo enhancement and editing workflow">
                </div>
                <div class="col-6">
                    <img class="img-fluid" src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/Real-Estate-Photography-Clipping.jpg" alt="Real estate photography clipping service showing property image enhancement and background optimization">
                </div>

            </div>

        </div>

        <?php /* <div class="row row-nav">
            <div class="col">
                <nav aria-label="Page navigation example">
                    <ul class="pagination">
                        <li class="page-item cus-nav-prev">
                            <a class="page-link" href="#" tabindex="-1"><img src="<?php bloginfo('template_url'); ?>/img/arrow-left.svg"> Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item cus-nav-next">
                            <a class="page-link" href="#">Next <img src="<?php bloginfo('template_url'); ?>/img/arrow-right.svg"></a>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="col text-end">
                <a href="#page-top" class="back-top">
                    <img src="<?php bloginfo('template_url'); ?>/img/back-top.svg">
                </a>
            </div>
        </div> */ ?>
    </div>
    </div>
</section>

<?php get_footer(); ?>