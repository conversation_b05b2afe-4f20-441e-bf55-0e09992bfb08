/*
Theme Name: KlayTech Starter BS4
Theme URI: https://www.klay.tech
Description: KlayTech Starter theme for WordPress
Author: <PERSON><PERSON><PERSON>
Author URI: https://www.sykot.com
Version: 1.0
Date: 27 January 2018
*/

@font-face {
    font-family: 'Museo Sans 500';
    src: url('fonts/MuseoSans-500Italic.woff2') format('woff2'),
        url('fonts/MuseoSans-500Italic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 500';
    src: url('fonts/MuseoSans-500.woff2') format('woff2'),
        url('fonts/MuseoSans-500.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 700';
    src: url('fonts/MuseoSans-700Italic.woff2') format('woff2'),
        url('fonts/MuseoSans-700Italic.woff') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 300';
    src: url('fonts/MuseoSans-300.woff2') format('woff2'),
        url('fonts/MuseoSans-300.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 900';
    src: url('fonts/MuseoSans-900.woff2') format('woff2'),
        url('fonts/MuseoSans-900.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 700';
    src: url('fonts/MuseoSans-700.woff2') format('woff2'),
        url('fonts/MuseoSans-700.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 100';
    src: url('fonts/MuseoSans-100Italic.woff2') format('woff2'),
        url('fonts/MuseoSans-100Italic.woff') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 100';
    src: url('fonts/MuseoSans-100.woff2') format('woff2'),
        url('fonts/MuseoSans-100.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Museo Sans 900';
    src: url('fonts/MuseoSans-900Italic.woff2') format('woff2'),
        url('fonts/MuseoSans-900Italic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

/* reset : dotted border */
a:focus {
    outline-style: none !important;
}
*:focus {
    outline: none !important;
    box-shadow: none !important;
}

html, body {
    font-family: 'Museo Sans', Helvetica, sans-serif;
    font-weight: 500;
    color: #4F3D40;
}

.btn-primary {
    background-color: #DAA900;
    border-color: #DAA900;
}
.btn-primary:hover {
    background-color: #c19700;
    border-color: #c19700;
}

.link-color-blue {
    color: #0080BB !important;
    font-weight: 600 !important;
}

.video {
    width: 100%;
    height: auto;
    object-fit: cover;
  }


/* nav */
.dropdown-menu {
    transition: opacity 0.3s ease-in-out;
}
.dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
}
.nav-top {
    padding-top: 15px;
}
.nav-top a {
    font-weight: 100;
    font-size: 14px;
}
.nav-item {
    padding-left: 15px;
    /* padding-right: 15px; */
}
.nav-main a {
    color: #4F3D40;
    font-weight: 600;
}
.nav-main a.btn {
    color: #fff;
}
.navbar-brand img {
    width: 210px;
    max-width: 100%;
}
.dropdown-item {
    font-weight: 400 !important;
    border-bottom: 1px solid #f4f4f4;
    font-size: 15px;
}
.dropdown-menu li:last-child .dropdown-item {
    border-bottom: 0px;
}

/* home intro */
#home-intro h1 {
    color: #0080BB;
    font-weight: 300;
    font-size: 48px;
}
#home-intro h1 span {
    color: #DAA900;
    font-weight: 500;
}

@media screen and (min-width:992px) {
    .home-intro-video {
        padding-left: 100px;
        padding-right: 100px;
    }
}

#why-choose h2 {
    color: #0080BB;
    font-size: 56px;
}
#why-choose p {
    font-size: 21px;
    font-weight: 300;
    text-align: justify;
}

#why-choose-icons h4 {
    color: #0080BB;
    font-size: 36px;
    padding-bottom: 50px;
}
#why-choose-icons p {
    margin-top: -25px;
    padding-bottom: 50px;
}
#why-choose-icons .col img {
    margin-bottom: 20px;
}
#why-choose-icons .col {
    font-size: 21px;
    font-weight: 600;
}
#why-choose-icons .col:nth-child(2), #why-choose-icons .col:nth-child(3), #why-choose-icons .col:nth-child(4) {
    border-left: 1px solid #73BEDD;
}

@media screen and (max-width:767px) {
    #why-choose p {
        font-size: 16px;
    }
    #why-choose-icons h4 {
        font-size: 28px;
    }
}

#vision {
    padding: 100px 0;
}
#vision .container {
    background-image: url(img/bg_vision.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}
#vision h6 {
    max-width: 1000px;
    padding: 50px 100px;
    color: #fff;
    font-size: 32px;
    line-height: 1.6em;
}

#our-offer h2 {
    color: #0080BB;
    font-size: 56px;
}
#our-offer p {
    font-size: 21px;
    font-weight: 300;
    text-align: justify;
}

@media screen and (max-width:767px) {
    #our-offer p {
        font-size: 16px;
    }
}
#our-offer-icons .video {
    width: 100%;
    max-width: 120px;
    height: auto;
    object-fit: cover;
    margin-bottom: 40px;
}
#our-offer-icons {
    color: #DAA900;
    font-size: 19px;
}
#our-offer-icons a {
    color: #DAA900;
    text-decoration: none;
}
#our-offer-icons .col {
    border: 1px solid #afafaf;
    margin: 50px;
    padding: 100px 50px;
    border-radius: 15px;
}
#steps .row {
    background-image: url(img/bg_steps.png);
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center;
    padding-left: 150px;
    padding-right: 150px;
}
#steps img {
    width: 100%;
}
#steps h3 {
    color: #0080BB;
    font-size: 56px;
}
#steps h4 {
    margin-bottom: 50px;
}
.steps-alt {
    margin-top: 100px;
}

@media screen and (max-width:767px) {
    #steps h3 {
        font-size: 42px;
    }
}

/* page about */
#page-head-bg {
    margin-top: 50px;
}
#page-head-bg .container {
    background-image: url(img/bg_vision.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    color: #fff;
    padding: 50px;
}
#page-head-bg .container p {
    max-width: 400px;
    font-weight: 300;
}

#page-lead {
    margin-top: 50px;
}
#page-lead p {
    font-size: 21px;
    font-weight: 300;
    text-align: justify;
}

#page-content p {
    font-weight: 300;
    text-align: justify;
}
#page-content h3 {
    color: #0080BB;
    font-weight: 600;
    margin-top: 50px;
}

#basic-content {
    margin-top: 100px;
    color: #86787B;
}
#basic-content p {
    font-weight: 300;
    text-align: justify;
}
#basic-content h1 {
    color: #0080BB;
    font-weight: 600;
    margin-bottom: 20px;
}
#basic-content h4 {
    margin-top: 50px;
}
#basic-content a {
    color: #DAA900;
    font-weight: 500;
    text-decoration: none;
}
#basic-content br {
    margin-bottom: 20px;
    display: block;
}

#basic-content h2 {
    color: #0080BB;
    font-weight: 300;
    margin-bottom: 20px;
    margin-top: 50px;
}
.content-faq .accordion-button::after {
    background-image: url(img/arrow-faq.svg);
}
.content-faq .accordion-button, .content-faq .accordion-body {
    color: #86787B;
}
.content-faq .accordion-body {
    font-weight: 300;
    text-align: justify;
}

/* contact */
#basic-contact {
    margin-top: 100px;
}
#basic-contact h3 {
    font-weight: 300;
    font-style: italic;
}
#basic-contact h1 {
    font-size: 96px;
    font-weight: 700;
    color: #0080BB;
    margin-bottom: 30px;
}
#basic-contact h1 span{
    color: #DAA900;
}
#basic-contact p {
    font-size: 21px;
    font-weight: 300;
}
#basic-contact h5 {
    margin-top: 10px;
}
#basic-contact .contant-number {
    margin-top: 50px;
}
#basic-contact .contant-number img {
    margin-right: 10px;
}
#basic-quote .upload-info {
    font-size: 15px;
}

/* quote */
#basic-quote {
    margin-top: 100px;
}
#basic-quote h1 {
    font-size: 56px;
    font-weight: 600;
    color: #0080BB;
    margin-bottom: 30px;
}
#basic-quote h1 span{
    color: #DAA900;
}
#basic-quote p {
    font-size: 19px;
    font-weight: 300;
}

@media screen and (min-width:992px) {
.form-basic {
    margin-left: 100px;
}
}
.form-basic .form-group {
    margin-bottom: 20px;
    font-weight: 300;
}
.form-basic .btn {
    text-transform: uppercase;
}

/* image page */
#image-content {
    margin-top: 100px;
}
#image-content h1 {
    font-size: 32px;
    color: #0080BB;
    margin-bottom: 50px;
}
#image-content h1 span {
    color: #DAA900;
}
#image-content h5 {
    margin-bottom: 50px;
    text-align: justify;
}
#image-content p {
    font-weight: 300;
    font-size: 19px;
    margin-bottom: 30px;
    text-align: justify;
}
#image-content .video {
    width: 100%;
    height: auto;
    object-fit: cover;
}
#image-layout-col {
    margin-top: 100px;
}
#image-layout-col .layout-col-head .layout-col-icon {
    max-width: 64px;
}
#image-layout-col .layout-col-head h3,
#image-layout-col .layout-col-head h1 {
    font-size: 36px;
    margin-top: 15px;
    margin-bottom: 30px;
}
#image-layout-col p {
    font-weight: 300;
    margin-bottom: 30px;
    text-align: justify;
}
#image-layout-col ul li {
    list-style: square;
    font-weight: 300;
}
#image-layout-col .pagination li {
    list-style: none !important;
}
.pagination .active>.page-link, .pagination .page-link.active {
    background-color: #DAA900;
    border-color: #DAA900;
    color: #fff;
}
.pagination .page-link {
    color: #DAA900;
}
.pagination .disabled>.page-link, .pagination .page-link.disabled {
    color: #999;
}

#image-layout-col .layout-col-middle {
    margin-top: 100px;
}
#image-layout-col .layout-col-middle h2 {
    color: #0080BB;
    margin-bottom: 30px;
}
#image-layout-col .layout-col-middle .row {
    margin-bottom: 50px;
}
#image-layout-col .layout-col {
    margin-top: 100px;
}
#image-layout-col h6 {
    font-size: 21px;
    margin-bottom: 20px;
}
#image-layout-col .layout-col p {
    font-size: 13px;
}
.layout-col-quote {
    margin-top: 50px;
}
.layout-col-alt p {
    min-height: 100px;
}
.layout-col-head-alt {
    margin-top: 50px;
}
.layout-col-head-alt-btm {
    margin-top: 50px;
    margin-bottom: 100px;
}

.video-layout-col .video {
    margin-bottom: 50px;
}

#page-head-bg.page-head-bg-portfolio .container p {
    max-width: 600px;
    margin: 0 auto;
}

#util-nav {
    margin-top: 100px;
}

.layout-col-portfolio .col-6 {
    margin-bottom: 50px;
    position: relative;
}
.layout-col-portfolio .col-6 h6 {
    position: absolute;
    bottom: 0px;
    text-align: center;
    /* width: 100%; */
    margin: 0;
    left: 25px;
    right: 25px;
    background-color: rgba(0, 0, 0, 0.3);
    padding-bottom: 15px;
    padding-top: 15px;
}
.layout-col-portfolio .col-6 a {
    color: #fff;
}

.layout-col-blog h6 {
    font-size: 24px !important;
    color: #0080BB;
    font-weight: 600;
    margin-top: 25px;
}
.layout-col-blog a {
    text-decoration: none;
    color: #DAA900;
    font-weight: 500;
}
.layout-col-blog .col-6 {
    margin-bottom: 50px;
}

.blog-post h1 {
    font-size: 48px;
    color: #0080BB;
    margin-bottom: 50px;
}
.blog-post h3 {
    font-size: 21px;
}
.blog-post img {
    margin-bottom: 50px;
}
.blog-post .img-block {
    display: block;
    clear: both;
}
.blog-post .img-block img {
    float: left;
    margin-right: 25px;
}
.blog-post a {
    color: #0080BB;
    font-weight: 500;
    text-decoration: none;
}
.title-color h4 {
    font-size: 17px;
    color: #0080BB;
}
.title-color h3 {
    color: #0080BB;
}

footer {
    margin-top: 150px;
    margin-bottom: 100px;
    color: #7D6D70;
}
footer h6 {
    font-weight: 600;
}
footer p {
    font-weight: 300;
}
footer h5 {
    font-size: 16px;
}
footer h5 img {
    margin-right: 10px;
}

footer .footer-payment h6 {
    font-weight: 300;
}
footer .footer-menu h6 {
    font-weight: 300;
}
footer .footer-menu ul {
    padding-left: 0;
    margin-left: 0;
}
footer .footer-menu ul li {
    list-style-type: none;
    margin-left: 0;
    padding-left: 0;
    padding-bottom: 10px;
}
footer .footer-menu ul li a {
    color: #4F3D40;
    text-decoration: none;
}
footer .footer-whatsapp img {
    width: 100%;
    max-width: 90px;
}


/* 30 Jan 2025 */
#util-nav select.form-control {
    background-image: url(img/arrow-g.svg);
    background-repeat: no-repeat;
    background-position: right center;
    font-weight: 300;
}
#util-nav input.cus-search {
    background-image: url(img/search.svg);
    background-repeat: no-repeat;
    background-position: 15px center;
    padding-left: 45px;
    font-weight: 300;
}
.pagination .cus-nav-prev a {
    color: #C0B8BA;
}
.pagination .cus-nav-next a {
    color: #C0B8BA;
}
.pagination .cus-nav-prev img {
    margin-right: 5px;
}
.pagination .cus-nav-next img {
    margin-left: 5px;
}
.pagination .page-link {
    border: 0 !important;
    color: #C0B8BA;
}
.pagination .active>.page-link, .pagination .page-link.active {
    background-color: #fff;
    color: #DAA900;
}
@media screen and (max-width:767px) {
    .row-nav .col {
        width: 100%;
        flex: none;
    }
}

@media screen and (max-width:767px) {
    .page-about-us-head,
    .page-about-us-head p.text-end {
        text-align: center !important;
    }
    .page-template-page-image-edit-1 .row,
    .page-template-page-image-edit-2 .row,
    .page-template-page-image-edit-3 .row,
    .page-template-page-image-edit .row,
    .page-template-page-video-production .row {
        flex: none !important;
    }
    .page-template-page-image-edit-1 .row .col,
    .page-template-page-image-edit-2 .row .col,
    .page-template-page-image-edit-3 .row .col,
    .page-template-page-image-edit .row .col,
    .page-template-page-video-production .row .col {
        width: 100%;
        flex: none !important;
    }
}

@media screen and (min-width:768px) {
    .post-img-size {
        padding-left: 50px;
        padding-right: 50px;
    }
}