<?php

	/*
		Template Name: Home
	*/

?>

<?php get_header(); ?>

<style>
/* Home Intro Slider Styles */
.home-intro-slider {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

#homeSlider {
    height: 100%;
}

.carousel-inner {
    height: 100%;
}

.carousel-inner .item {
    height: 100%;
    transition: transform 0.8s ease-in-out;
}

.slide-content {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.slide-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
}

/* Slide 1 Styles */
.slide-1 .slide-bg-image {
    animation: slideZoomIn 12s ease-out forwards;
}

@keyframes slideZoomIn {
    0% { transform: scale(1); }
    100% { transform: scale(1.08); }
}

/* Slide Overlay for Slides 2 & 3 */
.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px;
    z-index: 2;
}

/* Slide 2 Styles */
.slide-2 .slide-overlay {
    justify-content: space-between;
}

.slide-left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideInLeft 1s ease-out;
}

.slide-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideInRight 1s ease-out;
}

.slide-left-image, .slide-right-image {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    filter: drop-shadow(0 5px 15px rgba(0,0,0,0.3));
    transition: transform 0.3s ease;
}

.slide-left-text, .slide-right-text {
    max-width: 90%;
    max-height: 70%;
    object-fit: contain;
    filter: drop-shadow(0 3px 10px rgba(0,0,0,0.2));
}

/* Slide 3 Styles */
/* .slide-3 .slide-overlay {
    position: relative;
} */

.slide-logo {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 3;
    animation: fadeInDown 1s ease-out;
}

.slide-logo-image {
    max-width: 150px;
    max-height: 80px;
    object-fit: contain;
    filter: drop-shadow(0 3px 8px rgba(0,0,0,0.3));
}

/* Animation Keyframes */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.slide-left-image:hover, .slide-right-image:hover {
    transform: scale(1.05);
}

/* Carousel Controls Styling */
.carousel-control {
    background: linear-gradient(45deg, rgba(0,128,187,0.8), rgba(218,169,0,0.8));
    width: 60px;
    height: 60px;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.8;
    transition: all 0.3s ease;
}

.carousel-control:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

.carousel-control.left {
    left: 20px;
}

.carousel-control.right {
    right: 20px;
}

.carousel-control .glyphicon {
    font-size: 20px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

/* Carousel Indicators */
.carousel-indicators {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    margin-left: 0;
}

.carousel-indicators li {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.5);
    border: 2px solid rgba(255,255,255,0.8);
    transition: all 0.3s ease;
    margin: 0 6px;
    cursor: pointer;
    display: inline-block;
    text-indent: -999px;
    position: relative;
    z-index: 10;
}

.carousel-indicators li:hover {
    background-color: rgba(255,255,255,0.8);
    transform: scale(1.1);
}

.carousel-indicators .active {
    background-color: #DAA900;
    border-color: #DAA900;
    transform: scale(1.2);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .home-intro-slider {
        height: 350px;
    }

    .slide-overlay {
        padding: 20px;
        flex-direction: column;
        justify-content: center;
    }

    .slide-left, .slide-right {
        flex: none;
        margin: 10px 0;
    }

    .slide-logo {
        position: relative;
        top: auto;
        left: auto;
        text-align: center;
        margin-bottom: 20px;
    }

    .slide-logo-image {
        max-width: 120px;
        max-height: 60px;
    }

    .carousel-control {
        width: 40px;
        height: 40px;
    }

    .carousel-control .glyphicon {
        font-size: 16px;
    }
}

@media screen and (max-width: 480px) {
    .home-intro-slider {
        height: 280px;
    }

    .slide-overlay {
        padding: 15px;
    }

    .slide-logo-image {
        max-width: 100px;
        max-height: 50px;
    }
}
</style>

<section id="home-intro" class="p-5">

<div class="container">
    <?php /* <div class="home-intro-video">
        <video class="video" autoplay loop muted playsinline>
            <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/home_intro.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div> */?>
    <div class="home-intro-slider">
        <!-- Custom Slider -->
        <div id="homeSlider" class="carousel slide" data-ride="carousel">
            <!-- Indicators -->
            <ol class="carousel-indicators">
                <li data-target="#homeSlider" data-slide-to="0" class="active" role="button"></li>
                <li data-target="#homeSlider" data-slide-to="1" role="button"></li>
                <li data-target="#homeSlider" data-slide-to="2" role="button"></li>
            </ol>

            <!-- Slides -->
            <div class="carousel-inner">
                <!-- Slide 1: Single Image -->
                <div class="item active">
                    <div class="slide-content slide-1">
                        <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_1.jpg" alt="Slide 1" class="slide-bg-image">
                    </div>
                </div>

                <!-- Slide 2: Background + Left Image + Right Text -->
                <div class="item">
                    <div class="slide-content slide-2">
                        <img src="<?php bloginfo('template_url'); ?>/img/slides/bg.jpg" alt="Background" class="slide-bg-image">
                        <div class="slide-overlay">
                            <div class="slide-left">
                                <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_2_img.png" alt="Slide 2 Image" class="slide-left-image">
                            </div>
                            <div class="slide-right">
                                <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_2_text.svg" alt="Slide 2 Text" class="slide-right-text">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Background + Logo + Left Text + Right Image -->
                <div class="item">
                    <div class="slide-content slide-3">
                        <img src="<?php bloginfo('template_url'); ?>/img/slides/bg.jpg" alt="Background" class="slide-bg-image">
                        <div class="slide-overlay">
                            <div class="slide-logo">
                                <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_3_logo.svg" alt="Slide 3 Logo" class="slide-logo-image">
                            </div>
                            <div class="slide-left">
                                <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_3_text.svg" alt="Slide 3 Text" class="slide-left-text">
                            </div>
                            <div class="slide-right">
                                <img src="<?php bloginfo('template_url'); ?>/img/slides/slide_3_img.png" alt="Slide 3 Image" class="slide-right-image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Controls -->
            <a class="left carousel-control" href="#homeSlider" data-slide="prev">
                <span class="glyphicon glyphicon-chevron-left"></span>
            </a>
            <a class="right carousel-control" href="#homeSlider" data-slide="next">
                <span class="glyphicon glyphicon-chevron-right"></span>
            </a>
        </div>
    </div>
    <div class="home-intro-tag text-end pt-5">
        <h1>WE CREATE <span>VALUES</span></h1>
    </div>
</div>

</section>

<section id="why-choose" class="p-5">
<div class="container">
    <h2 class="text-center">Why choose us?</h2>
    <p>We approach each project as unique, utilizing various techniques and expertise to deliver excellent results on time and within budget. Attest Design's professional image editing, video editing and motion design services are the perfect solution for anyone looking to enhance their marketing concept. Our team of experts will refine your videos to ensure they perfectly complement your high-quality motions, providing crisp, high-quality image and video content that will captivate your audience. With our worldwide service, you can rest assured that your video will stand out from the crowd and leave a lasting impression on your viewers. Choose AttestDesign Ltd. and take your marketing to the next level!
    </p>
</div>
</section>

<section id="why-choose-icons" class="p-5">
<div class="container">
    <h4 class="text-center">We Aid In The Growth Of Good Businesses Into Great.</h4>
    <div class="row text-center">
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/icons/trans.svg" alt="Transparency icon - Always transparent business practices"><br>
            ALWAYS<br>TRANSPARENT
        </div>
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/icons/time.svg" alt="Time management icon - Great time management and delivery"><br>
            GREAT TIME<br>MANAGEMENT
        </div>
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/icons/quality.svg" alt="Quality guarantee icon - Quality guarantee on all services"><br>
            QUALITY<br>GUARANTEE
        </div>
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/icons/247.svg" alt="24/7 support icon - Round the clock customer support"><br>
            24/7<br>SUPPORT
        </div>
    </div>
</div>
</section>

<section id="vision">
<div class="container">
    <h6>Our vision is to be attested by every client by delivering service that meets their expectations and is as reliable as the North Star.</h6>
</div>
</section>

<section id="our-offer" class="p-5">
<div class="container">
    <h2 class="text-center">Our offered service</h2>
    <p>Integrating advanced technology with the expertise of skilled humans can lead to outstanding results, giving you a competitive edge in today's ever-changing market. We offer various services, including image editing and video production, to help you achieve your goals. Below is a summary of the services we provide:
    </p>
</div>
</section>

<section id="our-offer-icons" class="p-5">
<div class="container">
    <div class="row text-center">
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/clipping.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/image-editing#ClippingPath">Clipping Path</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/background.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/image-editing#BackgroundRemoval">Background Removal</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/ghost.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/image-editing#GhostMannequin">Ghost Mannequin</a>
        </div>
    </div>

    <div class="row text-center">
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/masking.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/image-editing#ImageMasking">Image Masking</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/retouching.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/photo-editing#PhotoRetouching">Photo Retouching Service</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/color.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/photo-editing#ColorCorrection">Color Correction</a>
        </div>
    </div>

    <div class="row text-center">
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/photography.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/photo-editing#PhotographyPostProcessing">Photography Post-Processing</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/ecom.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/e-commerce-product-image-editing/">E-commerce Product Image Editing</a>
        </div>
        <div class="col">
            <video class="video" autoplay loop muted playsinline>
                <source src="<?php echo get_option('home'); ?>/wp-content/uploads/2025/05/video.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video><br>
            <a href="<?php echo get_option('home'); ?>/video-production">Video Production</a>
        </div>
    </div>
</div>
</section>

<section id="steps">
<div class="container text-center">
    <h3>Simple 3-Step Order Process</h3>
    <h4>Get your service done in 3 simple steps-</h4>
    <div class="row">
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/quote.png" alt="Step 1: Get a quote - Request pricing for your project">
        </div>
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/upload.png" alt="Step 2: Upload files - Send us your images or videos">
        </div>
        <div class="col">
            <img src="<?php bloginfo('template_url'); ?>/img/download.png" alt="Step 3: Download results - Receive your edited files">
        </div>
    </div>
</div>
</section>

<script>
jQuery(document).ready(function($) {
    // Wait for DOM to be fully loaded
    setTimeout(function() {
        // Initialize carousel with custom settings
        $('#homeSlider').carousel({
            interval: 3000, // 3 seconds per slide
            pause: 'hover',
            wrap: true,
            keyboard: true
        });

        // Force start the carousel
        $('#homeSlider').carousel('cycle');

        // Manually handle indicator clicks
        $('.carousel-indicators li').click(function() {
            var slideIndex = $(this).data('slide-to');
            $('#homeSlider').carousel(slideIndex);
        });

    }, 100);

    // Add animation classes when slide changes
    $('#homeSlider').on('slide.bs.carousel', function (e) {
        var $animatingElements = $(e.relatedTarget).find('[class*="slide-"]');

        // Reset animations
        $animatingElements.removeClass('animated');

        // Add animation delay
        setTimeout(function() {
            $animatingElements.addClass('animated');
        }, 300);
    });

    // Debug: Check if carousel is working
    console.log('Carousel initialized');

    // Pause carousel on hover over controls
    $('.carousel-control, .carousel-indicators').hover(
        function() {
            $('#homeSlider').carousel('pause');
        },
        function() {
            $('#homeSlider').carousel('cycle');
        }
    );

    // Additional event listeners for indicators
    $('#homeSlider').on('slid.bs.carousel', function () {
        console.log('Slide changed');
        // Update active indicator
        var activeIndex = $('.carousel-inner .item.active').index();
        $('.carousel-indicators li').removeClass('active');
        $('.carousel-indicators li').eq(activeIndex).addClass('active');
    });

    // Add smooth transition effects
    $('#homeSlider').on('slid.bs.carousel', function () {
        var $activeSlide = $(this).find('.item.active');
        var slideClass = '';

        if ($activeSlide.find('.slide-1').length) {
            slideClass = 'slide-1-active';
        } else if ($activeSlide.find('.slide-2').length) {
            slideClass = 'slide-2-active';
        } else if ($activeSlide.find('.slide-3').length) {
            slideClass = 'slide-3-active';
        }

        // Add body class for slide-specific styling
        $('body').removeClass('slide-1-active slide-2-active slide-3-active').addClass(slideClass);
    });

    // Initialize first slide
    $('body').addClass('slide-1-active');

    // Touch/swipe support for mobile
    var startX = 0;
    var endX = 0;

    $('#homeSlider').on('touchstart', function(e) {
        startX = e.originalEvent.touches[0].clientX;
    });

    $('#homeSlider').on('touchend', function(e) {
        endX = e.originalEvent.changedTouches[0].clientX;
        handleSwipe();
    });

    function handleSwipe() {
        var threshold = 50; // Minimum swipe distance
        var diff = startX - endX;

        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                // Swipe left - next slide
                $('#homeSlider').carousel('next');
            } else {
                // Swipe right - previous slide
                $('#homeSlider').carousel('prev');
            }
        }
    }

    // Keyboard navigation
    jQuery(document).keydown(function(e) {
        if ($('#homeSlider').is(':visible')) {
            switch(e.which) {
                case 37: // left arrow
                    $('#homeSlider').carousel('prev');
                    break;
                case 39: // right arrow
                    $('#homeSlider').carousel('next');
                    break;
                default: return;
            }
            e.preventDefault();
        }
    });

    // Preload images for smooth transitions
    var imagesToPreload = [
        '<?php bloginfo('template_url'); ?>/img/slides/slide_1.jpg',
        '<?php bloginfo('template_url'); ?>/img/slides/bg.jpg',
        '<?php bloginfo('template_url'); ?>/img/slides/slide_2_img.png',
        '<?php bloginfo('template_url'); ?>/img/slides/slide_2_text.svg',
        '<?php bloginfo('template_url'); ?>/img/slides/slide_3_logo.svg',
        '<?php bloginfo('template_url'); ?>/img/slides/slide_3_text.svg',
        '<?php bloginfo('template_url'); ?>/img/slides/slide_3_img.png'
    ];

    imagesToPreload.forEach(function(src) {
        var img = new Image();
        img.src = src;
    });
});
</script>

<?php get_footer(); ?>
