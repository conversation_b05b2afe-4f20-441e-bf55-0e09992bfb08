<?php
/*
    Template Name: Promo Campaign
*/
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <?php
    // Meta Description for Promo Page
    $meta_description = 'Launch Special: Set your price and get rewarded. Transform your product images into sales machines with Attest Design professional editing services.';
    echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";
    ?>

    <title>Launch Special - Set Your Price | Attest Design</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="<?php bloginfo('stylesheet_url'); ?>?v=20250601" type="text/css" />
    <link rel="stylesheet" href="<?php bloginfo('template_url'); ?>/custom-web.css?v=2025" type="text/css" />
    <link rel="stylesheet" href="<?php bloginfo('template_url'); ?>/css/promo.css?v=2025" type="text/css" />

    <link rel="icon" type="image/png" href="<?php bloginfo('template_url'); ?>/img/logo-icon.png">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

<!-- Custom Promo Header (No default menu) -->
<header class="promo-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <a href="<?php echo get_option('home'); ?>">
                    <img src="<?php bloginfo('template_url'); ?>/img/logo_n.svg" alt="Attest Design" style="height: 40px;">
                </a>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-cta" data-bs-toggle="modal" data-bs-target="#getStartedModal">GET STARTED</button>
            </div>
        </div>
    </div>
</header>

<!-- Hero Section -->
<div class="container">
    <section class="promo-hero">
        
        <div class="row">
            <div class="col-lg-6">
                <p class="lead">Launching now:</p>
                <h1>Attest Design is <span class="highlight">LIVE</span></h1>
                <p class="lead-2nd">Open to claim your custom quote today</p>

                <div class="price-text">You Set the Price</div>
                <div class="delivery-text">we deliver perfection</div>

                <p class="promo-description">
                    To celebrate our launch, you name your price. Yes, really. Whether it's clipping path,
                    background removal, ghost mannequin, photo retouching, or any kind of video
                    production, you tell us what works for your budget and we'll bring your visuals to
                    life with expert precision.
                </p>

                <button type="button" class="btn btn-cta" data-bs-toggle="modal" data-bs-target="#getStartedModal">GET STARTED</button>
            </div>
        </div>
        
    </section>
</div>

<!-- Loyalty Discount Section -->
<section class="loyalty-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="loyalty-card">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h2 class="loyalty-title">LOYALTY DISCOUNT</h2>
                            <p class="loyalty-text">
                                Enjoy an additional 10% discount from the price you proposed on your first
                                order when you place a repeat order between August and November. At
                                Attest Design Ltd., we value long-term partnerships and reward your loyalty
                                with consistent savings.
                            </p>
                        </div>
                        <div class="col-lg-4 text-end">
                            <!-- <div class="discount-badge">
                                <div class="discount-number">10<span class="percent">%</span></div>
                                <div class="discount-label">
                                    <span class="additional">Additional</span><br>
                                    <span class="discount-text">DISCOUNT</span>
                                </div>
                            </div> -->
                            <img src="<?php bloginfo('template_url'); ?>/img/promo/10.svg" alt="10% Discount" class="img-fluid">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="why-choose-section">
    <div class="container">
        <h2>WHY CHOOSE US?</h2>

        <div class="row">
            <!-- First Row -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card teal">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-1.svg" class="img-fluid">
                    </div>
                    <h4 class="teal">Free Trial Available</h4>
                    <p>Still deciding? Let us show you what we can do. Send us a sample of your work and we will edit it for free. Just high-quality results that speak for themselves. Give us sample of your work and we will handle the rest.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card blue">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-2.svg" class="img-fluid">
                    </div>
                    <h4 class="blue">Money Back Guarantee</h4>
                    <p>We want you to feel confident working with us. If the final result doesn't meet your expectations, we will refund your payment. No hassle, no pressure. Your satisfaction is what matters most. Start your project with peace of mind.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card purple">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-3.svg" class="img-fluid">
                    </div>
                    <h4 class="purple">Smooth & Secure Payments</h4>
                    <p>We're concerned about making your payment process easy and secure. We accept globally trusted methods like Stripe, PayPal, Mastercard, Visa, and Bank Transfer. Your transactions and information are safe with our secure, encrypted payment gateway.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Second Row -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card pink">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-4.svg" class="img-fluid">
                    </div>
                    <h4 class="pink">Blazing Speed</h4>
                    <p>Attest Design Ltd. utilizes a high-speed FTP server, Amazon AWS VPS, and CDN integration for fast uploads, global file access, and strong data security. Your images and data are delivered quickly and safely.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card orange">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-5.svg" class="img-fluid">
                    </div>
                    <h4 class="orange">Easy Communication</h4>
                    <p>Stay connected effortlessly with our user-friendly communication system. We provide updates at every step, ensuring quick responses, clear information, and real human support for a smooth project from start to finish.</p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card gold">
                    <div class="icon-container">
                        <img src="<?php bloginfo('template_url'); ?>/img/promo/ico-6.svg" class="img-fluid">
                    </div>
                    <h4 class="gold">Great Time Management & Quality Guarantee</h4>
                    <p>We deliver high-quality image and video editing services on time with strict quality control. Our team follows an efficient workflow to ensure fast turnaround and consistent, professional results every time.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- What We Serve Section -->
<section class="what-we-serve-section">
    <div class="container">
        <!-- Header Section -->
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="serve-title">WHAT WE SERVE?</h2>
                <p class="serve-subtitle">
                    At Attest Design, we bring your visuals to life with expert precision. From e-commerce-ready images to dynamic video content, we offer a
                    full range of services tailored to your creative needs.
                </p>
            </div>
        </div>
    </div>

        <!-- Services Section -->
        <div class="services-showcase">
            <div class="services-bg">
                <div class="container">
                    <div class="row">
                        <div class="col-12 text-center mb-4">
                            <h3 class="services-title">Image Editing and Video Production Services</h3>
                        </div>
                    </div>

                    <div class="row justify-content-center">
                        <!-- Clipping Path -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="service-card">
                                <div class="service-image">
                                    <!-- Bicycle SVG -->
                                    <svg viewBox="0 0 300 200" class="service-svg">
                                        <defs>
                                            <pattern id="transparency" patternUnits="userSpaceOnUse" width="20" height="20">
                                                <rect width="10" height="10" fill="#f0f0f0"/>
                                                <rect x="10" y="10" width="10" height="10" fill="#f0f0f0"/>
                                                <rect x="10" y="0" width="10" height="10" fill="#ffffff"/>
                                                <rect x="0" y="10" width="10" height="10" fill="#ffffff"/>
                                            </pattern>
                                        </defs>
                                        <rect width="300" height="200" fill="url(#transparency)"/>
                                        <!-- Bicycle Frame -->
                                        <path d="M80 120 L140 80 L180 80 L220 120 L180 120 L140 140 Z" fill="#2196F3" stroke="#1976D2" stroke-width="3"/>
                                        <!-- Wheels -->
                                        <circle cx="80" cy="140" r="25" fill="none" stroke="#333" stroke-width="4"/>
                                        <circle cx="220" cy="140" r="25" fill="none" stroke="#333" stroke-width="4"/>
                                        <!-- Spokes -->
                                        <g stroke="#666" stroke-width="1">
                                            <line x1="80" y1="115" x2="80" y2="165"/>
                                            <line x1="55" y1="140" x2="105" y2="140"/>
                                            <line x1="220" y1="115" x2="220" y2="165"/>
                                            <line x1="195" y1="140" x2="245" y2="140"/>
                                        </g>
                                        <!-- Handlebars -->
                                        <line x1="140" y1="80" x2="140" y2="60" stroke="#333" stroke-width="3"/>
                                        <line x1="130" y1="60" x2="150" y2="60" stroke="#333" stroke-width="3"/>
                                        <!-- Seat -->
                                        <ellipse cx="180" cy="75" rx="15" ry="5" fill="#333"/>
                                    </svg>
                                </div>
                                <a href="<?php echo get_option('home'); ?>/image-editing#ClippingPath" class="service-label">Clipping Path</a>
                            </div>
                        </div>

                        <!-- Background Removal -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="service-card">
                                <div class="service-image">
                                    <!-- Camera SVG -->
                                    <svg viewBox="0 0 300 200" class="service-svg">
                                        <rect width="300" height="200" fill="url(#transparency)"/>
                                        <!-- Camera Body -->
                                        <rect x="50" y="80" width="200" height="80" rx="10" fill="#333" stroke="#222" stroke-width="2"/>
                                        <!-- Lens -->
                                        <circle cx="150" cy="120" r="35" fill="#444" stroke="#222" stroke-width="3"/>
                                        <circle cx="150" cy="120" r="25" fill="#666" stroke="#333" stroke-width="2"/>
                                        <circle cx="150" cy="120" r="15" fill="#222"/>
                                        <!-- Flash -->
                                        <rect x="200" y="85" width="15" height="10" rx="2" fill="#888"/>
                                        <!-- Viewfinder -->
                                        <rect x="80" y="70" width="40" height="15" rx="3" fill="#555"/>
                                        <!-- Camera Strap -->
                                        <rect x="60" y="75" width="8" height="15" rx="4" fill="#666"/>
                                        <rect x="232" y="75" width="8" height="15" rx="4" fill="#666"/>
                                        <!-- Scattered Elements -->
                                        <rect x="270" y="50" width="20" height="15" rx="2" fill="#888" opacity="0.7"/>
                                        <circle cx="40" cy="170" r="8" fill="#666" opacity="0.7"/>
                                        <rect x="20" y="40" width="15" height="10" rx="2" fill="#777" opacity="0.7"/>
                                    </svg>
                                </div>
                                <a href="<?php echo get_option('home'); ?>/image-editing#BackgroundRemoval" class="service-label">Background Removal</a>
                            </div>
                        </div>

                        <!-- Ghost Mannequin Effect -->
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="service-card">
                                <div class="service-image">
                                    <!-- Dress SVG -->
                                    <svg viewBox="0 0 300 200" class="service-svg">
                                        <rect width="300" height="200" fill="url(#transparency)"/>
                                        <!-- Dress -->
                                        <path d="M150 40 L130 60 L120 80 L110 120 L100 160 L120 180 L180 180 L200 160 L190 120 L180 80 L170 60 Z" fill="#2c2c2c" stroke="#1a1a1a" stroke-width="2"/>
                                        <!-- Dress Details -->
                                        <path d="M130 60 L170 60 L165 70 L135 70 Z" fill="#1a1a1a"/>
                                        <line x1="120" y1="100" x2="180" y2="100" stroke="#1a1a1a" stroke-width="1"/>
                                        <line x1="115" y1="140" x2="185" y2="140" stroke="#1a1a1a" stroke-width="1"/>
                                        <!-- Sleeves (transparent/ghost effect) -->
                                        <path d="M130 60 L110 70 L105 90 L115 95 L120 80" fill="none" stroke="#666" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
                                        <path d="M170 60 L190 70 L195 90 L185 95 L180 80" fill="none" stroke="#666" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
                                        <!-- Neckline (ghost effect) -->
                                        <ellipse cx="150" cy="50" rx="12" ry="8" fill="none" stroke="#666" stroke-width="2" stroke-dasharray="3,3" opacity="0.5"/>
                                    </svg>
                                </div>
                                <a class="service-label">Ghost Mannequin Effect</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
</section>

<!-- Don't Miss Section -->
<section class="final-cta-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12">
                <h2>DON'T MISS THE LAUNCH OFFER</h2>

                <div class="cta-tags">
                    <span class="cta-tag">Set your price today</span>
                    <span class="cta-tag">Get rewarded</span>
                </div>

                <div class="cta-tags">
                    <span class="cta-tag">Transform your product images into sales machines</span>
                </div>

                <div class="text-center" style="margin-top: 30px;">
                    <button type="button" class="btn btn-cta" data-bs-toggle="modal" data-bs-target="#getStartedModal">GET STARTED</button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Get Started Modal -->
<div class="modal fade" id="getStartedModal" tabindex="-1" aria-labelledby="getStartedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-5 pb-5">
                <div class="text-center mb-4">
                    <h2 class="modal-title text-primary fw-bold mb-3" id="getStartedModalLabel">
                        READY TO CLAIM THIS OFFER?
                    </h2>
                    <p class="modal-subtitle text-muted fs-5 fst-italic">
                        Submit your details to get started
                    </p>
                </div>

                <form id="getStartedForm" class="needs-validation" novalidate>
                    <div class="mb-4">
                        <input type="text" class="form-control form-control-lg" id="fullName" name="fullName" placeholder="Full Name *" required>
                        <div class="invalid-feedback">
                            Please provide your full name.
                        </div>
                    </div>

                    <div class="mb-4">
                        <input type="email" class="form-control form-control-lg" id="email" name="email" placeholder="Email *" required>
                        <div class="invalid-feedback">
                            Please provide a valid email address.
                        </div>
                    </div>

                    <div class="mb-4">
                        <input type="tel" class="form-control form-control-lg" id="phoneNumber" name="phoneNumber" placeholder="Phone Number">
                    </div>

                    <div class="text-center mb-4">
                        <p class="text-muted">
                            Once you fill in the form, our team will contact you to confirm your custom quote and kick off your project.
                        </p>
                    </div>

                    <div class="text-center mb-4">
                        <button type="submit" class="btn btn-warning btn-lg px-5 py-3 fw-bold text-uppercase">
                            SUBMIT
                        </button>
                    </div>

                    <div class="text-center">
                        <small class="text-muted">
                            * We will never share your info with anyone
                        </small>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Custom Footer (No default footer) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

<script>
// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling behavior
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Scroll animations removed as requested
});

// Handle modal form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('getStartedForm');
    const modal = document.getElementById('getStartedModal');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Remove previous validation classes
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.classList.remove('is-invalid', 'is-valid');
            });

            // Validate form
            let isValid = true;
            const fullName = document.getElementById('fullName');
            const email = document.getElementById('email');
            const phoneNumber = document.getElementById('phoneNumber');

            // Validate full name
            if (!fullName.value.trim()) {
                fullName.classList.add('is-invalid');
                isValid = false;
            } else {
                fullName.classList.add('is-valid');
            }

            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email.value.trim() || !emailRegex.test(email.value)) {
                email.classList.add('is-invalid');
                isValid = false;
            } else {
                email.classList.add('is-valid');
            }

            // Phone number is optional, but validate format if provided
            if (phoneNumber.value.trim()) {
                phoneNumber.classList.add('is-valid');
            }

            if (isValid) {
                // Prepare form data
                const formData = {
                    fullName: fullName.value.trim(),
                    email: email.value.trim(),
                    phoneNumber: phoneNumber.value.trim(),
                    source: 'Promo Page - Launch Offer',
                    timestamp: new Date().toISOString()
                };

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'SUBMITTING...';
                submitBtn.disabled = true;

                // Simulate form submission (replace with actual API call)
                setTimeout(() => {
                    // Reset form
                    form.reset();
                    inputs.forEach(input => {
                        input.classList.remove('is-invalid', 'is-valid');
                    });

                    // Close modal
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();

                    // Show success message
                    alert('Thank you! Your details have been submitted successfully. Our team will contact you soon to discuss your custom quote.');

                    // Reset button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;

                    // You can replace the alert with a more elegant success modal or notification
                    console.log('Form submitted:', formData);

                    // Here you would typically send the data to your server
                    // fetch('/api/submit-quote-request', {
                    //     method: 'POST',
                    //     headers: { 'Content-Type': 'application/json' },
                    //     body: JSON.stringify(formData)
                    // });

                }, 2000);
            }
        });
    }

    // Track modal opens
    if (modal) {
        modal.addEventListener('show.bs.modal', function() {
            console.log('Get Started modal opened');
            // You can add analytics tracking here
        });
    }
});
</script>

<?php wp_footer(); ?>

</body>
</html>
