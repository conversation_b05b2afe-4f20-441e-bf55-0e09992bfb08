<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <?php
    // Meta Description - ACF Integration
    $meta_description = '';

    if (is_home() || is_front_page()) {
        // Homepage - check for ACF field, fallback to site description
        $meta_description = function_exists('get_field') ? get_field('meta_description', get_option('page_on_front')) : false;
        if (empty($meta_description)) {
            $meta_description = get_bloginfo('description');
        }
    } elseif (is_single() || is_page()) {
        // Single post or page - check for ACF field, fallback to excerpt
        $meta_description = function_exists('get_field') ? get_field('meta_description') : false;
        if (empty($meta_description)) {
            $meta_description = get_the_excerpt();
        }
    } elseif (is_category()) {
        // Category page - check for ACF field on category, fallback to category description
        $category = get_queried_object();
        $meta_description = function_exists('get_field') ? get_field('meta_description', $category) : false;
        if (empty($meta_description)) {
            $meta_description = category_description();
        }
    } elseif (is_tag()) {
        // Tag page - check for ACF field on tag, fallback to tag description
        $tag = get_queried_object();
        $meta_description = function_exists('get_field') ? get_field('meta_description', $tag) : false;
        if (empty($meta_description)) {
            $meta_description = tag_description();
        }
    } elseif (is_author()) {
        // Author page - check for ACF field on user, fallback to author bio
        $author = get_queried_object();
        $meta_description = function_exists('get_field') ? get_field('meta_description', 'user_' . $author->ID) : false;
        if (empty($meta_description)) {
            $meta_description = get_the_author_meta('description', $author->ID);
        }
    } elseif (is_search()) {
        // Search results page
        $meta_description = 'Search results for: ' . get_search_query();
    } else {
        // Fallback for other pages
        $meta_description = get_bloginfo('description');
    }

    // Clean up and limit meta description
    if (!empty($meta_description)) {
        $meta_description = wp_strip_all_tags($meta_description);
        $meta_description = html_entity_decode($meta_description, ENT_QUOTES, 'UTF-8');
        $meta_description = str_replace(array("\r", "\n", "\t"), ' ', $meta_description);
        $meta_description = preg_replace('/\s+/', ' ', $meta_description);
        $meta_description = trim($meta_description);

        // Limit to 160 characters for optimal SEO
        if (strlen($meta_description) > 160) {
            $meta_description = substr($meta_description, 0, 157) . '...';
        }

        // Use a safer escaping method that doesn't re-encode apostrophes
        $safe_meta = str_replace('"', '&quot;', $meta_description);
        echo '<meta name="description" content="' . $safe_meta . '">' . "\n";
    }
    ?>

    <?php
    // Canonical URL - ACF Integration
    $canonical_url = '';

    if (is_page()) {
        // For pages - check for custom canonical URL from ACF
        $custom_canonical = function_exists('get_field') ? get_field('canonical_url') : false;
        if (!empty($custom_canonical)) {
            $canonical_url = esc_url($custom_canonical);
        } else {
            // Use default page URL
            $canonical_url = get_permalink();
        }
    } elseif (is_single()) {
        // For single posts - use post permalink
        $canonical_url = get_permalink();
    } elseif (is_home() || is_front_page()) {
        // For homepage
        $canonical_url = home_url('/');
    } elseif (is_category()) {
        // For category pages
        $canonical_url = get_category_link(get_queried_object_id());
    } elseif (is_tag()) {
        // For tag pages
        $canonical_url = get_tag_link(get_queried_object_id());
    } elseif (is_author()) {
        // For author pages
        $canonical_url = get_author_posts_url(get_queried_object_id());
    } elseif (is_search()) {
        // For search pages - no canonical URL
        $canonical_url = '';
    } else {
        // For other pages - try to get current URL
        $canonical_url = home_url(add_query_arg(array(), $wp->request));
    }

    // Output canonical tag if URL is available
    if (!empty($canonical_url)) {
        echo '<link rel="canonical" href="' . esc_url($canonical_url) . '">' . "\n";
    }
    ?>

	<?php if (is_search()) { ?>
	   <meta name="robots" content="noindex, nofollow" />
	<?php } ?>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="<?php bloginfo('stylesheet_url'); ?>?v=20250702" type="text/css" />
    <link rel="stylesheet" href="<?php bloginfo('template_url'); ?>/custom-web.css?v=2025" type="text/css" />
    <!-- <link rel="stylesheet" href="https://cdn.attestdesign.com/custom-web.css?v=2" type="text/css" /> -->
    
    <link rel="icon" type="image/png" href="<?php bloginfo('template_url'); ?>/img/logo-icon.png">

	<link rel="pingback" href="<?php bloginfo('pingback_url'); ?>" />

	<?php if ( is_singular() ) wp_enqueue_script( 'comment-reply' ); ?>

	<?php wp_head(); ?>
</head>

<body id="page-top" <?php body_class(); ?>>

    <nav class="nav-top">
        <div class="container d-flex flex-wrap">
            <ul class="nav ms-auto">
                <li class="nav-item"><a href="<?php echo get_option('home'); ?>/blog" class="nav-link link-body-emphasis px-2">BLOG</a></li>
                <li class="nav-item"><a href="<?php echo get_option('home'); ?>/faq" class="nav-link link-body-emphasis px-2">FAQ</a></li>
                <li class="nav-item"><a href="https://dashboard.attestdesign.com/login" class="nav-link link-body-emphasis px-2">LOGIN</a></li>
                <!-- <li class="nav-item"><a href="#" class="nav-link link-body-emphasis px-2">SEARCH</a></li> -->
            </ul>
        </div>
    </nav>

    <nav class="navbar navbar-expand-lg nav-main">
        <div class="container">
            <a class="navbar-brand" href="<?php echo get_option('home'); ?>">
                <img src="<?php bloginfo('template_url'); ?>/img/logo_n.svg" alt="Attest Design - Professional Photo Editing and Video Production Services" class="d-inline-block align-top">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo get_option('home'); ?>/about-us">About Us</a>
                    </li>
                    <li class="nav-item dropdown" data-bs-auto-close="outside">
                        <a class="nav-link dropdown-toggle" href="<?php echo get_option('home'); ?>/image-editing-all" id="navbarDropdown1" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Image Editing
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown1">
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/image-editing#ClippingPath">Clipping Path</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/image-editing#BackgroundRemoval">Background Removal</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/image-editing#GhostMannequin">Ghost Mannequin</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/image-editing#ImageMasking">Image Masking</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/photo-editing/#PhotoRetouching">Photo Retouching Service</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/photo-editing/#ColorCorrection">Color Correction</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/photo-editing/#PhotographyPostProcessing">Photography Post-Processing</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/e-commerce-product-image-editing/">E-commerce Product Image Editing</a></li>
                            <li><a class="dropdown-item" href="<?php echo get_option('home'); ?>/image-editing-all">View All</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo get_option('home'); ?>/video-production">Video Production</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo get_option('home'); ?>/portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary" href="https://dashboard.attestdesign.com/quote">Get A Quote</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>