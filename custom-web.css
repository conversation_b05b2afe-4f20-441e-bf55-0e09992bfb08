@media screen and (max-width:767px) {
    .navbar-toggler {
        border: 0;
        padding-top: 0;
        padding-bottom: 0;
        margin-top: -5px;
    }
    .navbar-brand img {
        width: 100%;
        max-width: 240px;
        margin-top: -5px;
    }
    #home-intro.p-5,
    #why-choose.p-5,
    #our-offer.p-5,
    #our-offer-icons.p-5 {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
    #home-intro.p-5 {
        padding-top: 20px !important;
    }
    .home-intro-tag.pt-5,
    #why-choose.p-5 {
        padding-top: 15px !important;
    }
    #home-intro h3 {
        font-size: 32px;
    }
    #why-choose h2,
    #our-offer h2 {
        font-size: 42px;
    }
    #why-choose-icons .col:nth-child(2), #why-choose-icons .col:nth-child(3), #why-choose-icons .col:nth-child(4) {
        border-left: 0;
    }
    #why-choose-icons .col img {
        max-width: 100%;
    }
    #why-choose-icons .col {
        width: 50%;
        padding: 20px;
        padding-bottom: 50px;
        font-size: 14px;
    }
    #vision {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    #vision h6 {
        padding: 50px 15px;
        font-size: 24px;
    }
    #our-offer-icons .col {
        margin: 0;
        margin-bottom: 25px;
        width: 100%;
        flex: none;
    }
    #steps .row {
        padding-left: 0;
        padding-right: 0;
        background-image: none;
    }
    #steps .row .col {
        min-width: 100%;
        flex: none;
    }
    #steps .row .col img {
        width: 200px;
        margin-bottom: 25px;
    }
    footer h6 {
        margin-top: 15px;
    }
    footer .col {
        width: 100%;
        flex: none !important;
    }
    .footer-payment h6+img {
        width: 100%;
    }
}

@media screen and (min-width:992px) {
    #steps .row {
        background-size: 90%;
    }
    #steps img {
        padding: 30px;
    }
}

select#discovery-call {
    background-image: url(https://attestdesign.com/wp-content/themes/ktad/img/arrow-g.svg);
    background-repeat: no-repeat;
    background-position: right center;
    font-weight: 300;
}

@media screen and (max-width:767px) {
    .contact-us-block .col {
        flex: none;
        margin-bottom: 25px
    }
    .contact-us-block h3.text-center,
    .contact-us-block h1.text-end {
        text-align: left !important;
    }
    .contact-us-block h1.text-end {
        font-size: 72px !important; 
    }
}