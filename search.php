<?php get_header(); ?>

<section id="page-head-bg" class="page-head-bg-portfolio">
    <div class="container">
        <div class="row">
            <div class="col text-center">
                <h1>Search Results</h1>
                <p>
                    <?php if (get_search_query()) : ?>
                        Search results for: "<strong><?php echo get_search_query(); ?></strong>"
                    <?php else : ?>
                        Search results
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
</section>

<section id="util-nav">
    <div class="container">
        <div class="row">
            <div class="col">
                <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="cus-search form-control" placeholder="Search topic or keyword" aria-label="Search" name="s" value="<?php echo get_search_query(); ?>">
                </form>
            </div>
            <div class="col"></div>
            <div class="col">
                <form>
                    <div class="form-group">
                        <select class="form-control" id="category-dropdown" onchange="navigateToCategory()">
                            <option value="">Category</option>
                            <?php
                            $categories = get_categories(array(
                                'hide_empty' => 0, // Show empty categories too
                                'exclude' => array(1, 2),
                                'orderby' => 'name',
                                'order' => 'ASC'
                            ));

                            foreach($categories as $category) {
                                echo '<option value="' . get_category_link($category->term_id) . '">' . $category->name . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </form>

                <script>
                function navigateToCategory() {
                    var dropdown = document.getElementById('category-dropdown');
                    var url = dropdown.options[dropdown.selectedIndex].value;
                    if (url) {
                        window.location.href = url;
                    }
                }
                </script>
            </div>
        </div>
    </div>
</section>

<section id="image-layout-col">
    <div class="container">
        <div class="container layout-col-middle layout-col-blog">

            <div class="row layout-col">

            <?php if (have_posts()) : ?>

                <?php while (have_posts()) : the_post(); ?>

                <div class="col-6">
                    <a href="<?php the_permalink() ?>">
                        <?php if (has_post_thumbnail()) : ?>
                            <img class="img-fluid" src="<?php the_post_thumbnail_url('full');?>" alt="<?php echo esc_attr(get_the_title()); ?> - Featured image">
                        <?php else : ?>
                            <img class="img-fluid" src="https://dummyimage.com/600x400/cccccc/ffffff&text=No+Image" alt="No image available for <?php echo esc_attr(get_the_title()); ?>">
                        <?php endif; ?>
                    </a>
                    <h6>
                        <a class="link-color-blue" href="<?php the_permalink() ?>">
                            <?php the_title(); ?>
                        </a>
                        </h6>
                    <p><?php the_excerpt(); ?></p>
                    <p>by <?php the_author() ?> • <?php the_time('F jS, Y') ?></p>
                    <p><a href="<?php the_permalink() ?>">Read more...</a></p>
                </div>

                <?php endwhile; ?>

            <?php else : ?>

                <div class="col-12 text-center">
                    <h2>No posts found</h2>
                    <p class="text-center">Sorry, no posts matched your search criteria. Please try again with different keywords.</p>
                </div>

            <?php endif; ?>

            </div>

        </div>
    </div>
</section>

<?php get_footer(); ?>
